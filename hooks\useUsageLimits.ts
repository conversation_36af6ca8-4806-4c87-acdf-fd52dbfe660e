import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  canUseFeature,
  trackFeatureUsage,
  getDailyUsage,
  startLiveSession,
  endLiveSession,
  getActiveLiveSessions,
  FeatureType,
  DailyUsageData,
  LiveSessionData
} from '../lib/supabase';
import { Alert } from 'react-native';
import { useLanguage } from '../contexts/LanguageContext';
import { useCaptcha } from './useCaptcha';

// Feature limits based on subscription tier
const FEATURE_LIMITS = {
  free: {
    [FeatureType.PILL_SCAN]: 10,
    [FeatureType.LIVE_PILL_SCAN]: 0, // Not available
    [FeatureType.NOTE_ANALYSIS]: 10,
  },
  pro: {
    [FeatureType.PILL_SCAN]: Infinity,
    [FeatureType.LIVE_PILL_SCAN]: 5,
    [FeatureType.NOTE_ANALYSIS]: 0, // Not available
  },
  premium: {
    [FeatureType.PILL_SCAN]: Infinity,
    [FeatureType.LIVE_PILL_SCAN]: Infinity,
    [FeatureType.NOTE_ANALYSIS]: Infinity,
  },
  admin: {
    [FeatureType.PILL_SCAN]: Infinity,
    [FeatureType.LIVE_PILL_SCAN]: Infinity,
    [FeatureType.NOTE_ANALYSIS]: Infinity,
  }
};

// Live session time limit in seconds (5 minutes)
const LIVE_SESSION_TIME_LIMIT = 300; // 5 minutes for all users (enforced in backend)

export const useUsageLimits = () => {
  const { user, isAnonymous, signInAnonymously } = useAuth();
  const { t } = useLanguage();
  const {
    isCaptchaModalVisible,
    showCaptchaModal,
    handleCaptchaVerify,
    closeCaptchaModal
  } = useCaptcha();
  const [dailyUsage, setDailyUsage] = useState<DailyUsageData[]>([]);
  const [activeSession, setActiveSession] = useState<LiveSessionData | null>(null);
  const [sessionTimeRemaining, setSessionTimeRemaining] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Get user's subscription tier
  const userTier = user?.subscription_tier || 'free';

  // Check if user is on a paid tier (pro, premium, or admin)
  const isPaidUser = userTier !== 'free';

  // Check if user is on a specific tier
  const isProUser = userTier === 'pro';
  const isPremiumUser = userTier === 'premium';
  const isAdminUser = userTier === 'admin';

  // Load daily usage data
  const loadDailyUsage = useCallback(async () => {
    if (!user) return;

    try {
      const usageData = await getDailyUsage(user.id);
      setDailyUsage(usageData);
    } catch (error) {
      console.error('Error loading daily usage:', error);
    }
  }, [user]);

  // Load active live sessions
  const loadActiveSessions = useCallback(async () => {
    if (!user) return;

    try {
      const sessions = await getActiveLiveSessions(user.id);
      if (sessions.length > 0) {
        setActiveSession(sessions[0]);
      } else {
        setActiveSession(null);
      }
    } catch (error) {
      console.error('Error loading active sessions:', error);
    }
  }, [user]);

  // Check if user can use a feature
  const checkFeatureAccess = useCallback(async (featureType: FeatureType): Promise<boolean> => {
    // Check if the feature is available for the user's tier
    const tierLimits = FEATURE_LIMITS[userTier as keyof typeof FEATURE_LIMITS] || FEATURE_LIMITS.free;
    const featureLimit = tierLimits[featureType];

    // If feature limit is 0, the feature is not available for this tier
    if (featureLimit === 0) {
      Alert.alert(
        t('featureNotAvailableTitle'),
        t('featureNotAvailableMessage', {
          feature: featureType === FeatureType.PILL_SCAN
            ? "Pill Count"
            : featureType === FeatureType.LIVE_PILL_SCAN
              ? "Live Pill Count"
              : t('noteAnalysis'),
          tier: userTier === 'free'
            ? t('freeTier')
            : userTier === 'pro'
              ? t('proTier')
              : t('premiumTier')
        }),
        [{ text: t('ok') }]
      );
      return false;
    }

    // Admin users always have access
    if (isAdminUser) return true;

    // Premium users always have access to all features
    if (isPremiumUser) return true;

    // Pro users have unlimited access to pill scan but limited access to live pill scan
    if (isProUser) {
      // For live pill scan, we need to check the usage count
      if (featureType === FeatureType.LIVE_PILL_SCAN) {
        // Continue with the check below
      } else if (featureType === FeatureType.PILL_SCAN) {
        return true;
      } else if (featureType === FeatureType.NOTE_ANALYSIS) {
        // Pro users don't have access to note analysis
        Alert.alert(
          t('featureNotAvailableTitle'),
          t('upgradeForFeatureMessage', {
            feature: t('noteAnalysis'),
            tier: t('premiumTier')
          }),
          [{ text: t('ok') }]
        );
        return false;
      }
    }

    // If no user, try to sign in anonymously with CAPTCHA
    if (!user) {
      try {
        console.log('No user detected, attempting anonymous sign-in with CAPTCHA');

        // Show CAPTCHA modal and wait for verification
        try {
          // Show the CAPTCHA modal and get the token
          const captchaToken = await showCaptchaModal();
          console.log('CAPTCHA verified, proceeding with anonymous sign-in');

          // Attempt anonymous sign-in with the CAPTCHA token
          const { success, message } = await signInAnonymously(captchaToken);

          if (!success) {
            console.error('Anonymous sign-in failed:', message);
            Alert.alert(
              t('usageLimitTitle'),
              t('anonymousAuthFailed'),
              [{ text: t('ok') }]
            );
            return false;
          }

          // If we successfully signed in anonymously, we'll continue below
          // The user state will be updated by the auth context
          console.log('Anonymous sign-in successful');
          return true;
        } catch (error) {
          // This will happen if the user cancels the CAPTCHA
          console.log('CAPTCHA verification cancelled or failed');
          return false;
        }
      } catch (error) {
        console.error('Error during anonymous sign-in:', error);
        Alert.alert(
          t('usageLimitTitle'),
          t('anonymousAuthFailed'),
          [{ text: t('ok') }]
        );
        return false;
      }
    }

    setIsLoading(true);
    try {
      const canUse = await canUseFeature(user.id, featureType);

      if (!canUse) {
        // Get the feature limit for the user's tier
        const tierLimits = FEATURE_LIMITS[userTier as keyof typeof FEATURE_LIMITS] || FEATURE_LIMITS.free;
        const featureLimit = tierLimits[featureType];

        // Show limit reached message
        Alert.alert(
          t('usageLimitTitle'),
          t('usageLimitReached', {
            feature: featureType === FeatureType.PILL_SCAN
              ? "Pill Count"
              : featureType === FeatureType.LIVE_PILL_SCAN
                ? "Live Pill Count"
                : t('noteAnalysis'),
            limit: featureLimit,
            tier: userTier === 'free'
              ? t('freeTier')
              : userTier === 'pro'
                ? t('proTier')
                : t('premiumTier')
          }),
          [
            { text: t('ok') },
            userTier === 'free' ? {
              text: t('upgradeToPro'),
              onPress: () => {
                // Navigate to subscription page
                // This will be implemented when Stripe is integrated
                console.log('Navigate to subscription page');
              }
            } : null,
            userTier === 'pro' && featureType === FeatureType.LIVE_PILL_SCAN ? {
              text: t('upgradeToPremium'),
              onPress: () => {
                // Navigate to subscription page
                // This will be implemented when Stripe is integrated
                console.log('Navigate to subscription page');
              }
            } : null
          ].filter(Boolean) // Remove null buttons
        );
      }

      return canUse;
    } catch (error) {
      console.error(`Error checking feature access for ${featureType}:`, error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user, isPaidUser, t, signInAnonymously, showCaptchaModal]);

  // Track feature usage
  const trackUsage = useCallback(async (featureType: FeatureType): Promise<boolean> => {
    // Paid users don't need to track usage for limits
    if (isPaidUser) return true;

    // If no user, try to sign in anonymously with CAPTCHA first
    if (!user) {
      try {
        // Show CAPTCHA modal and wait for verification
        try {
          // Show the CAPTCHA modal and get the token
          const captchaToken = await showCaptchaModal();
          console.log('CAPTCHA verified, proceeding with anonymous sign-in for tracking');

          // Attempt anonymous sign-in with the CAPTCHA token
          const { success } = await signInAnonymously(captchaToken);
          if (!success) return false;
          // Continue with the new anonymous user
        } catch (error) {
          // This will happen if the user cancels the CAPTCHA
          console.log('CAPTCHA verification cancelled or failed');
          return false;
        }
      } catch (error) {
        console.error('Error during anonymous sign-in for tracking:', error);
        return false;
      }
    }

    // At this point, we should have a user (either existing or anonymous)
    if (!user) {
      console.error('Still no user after anonymous sign-in attempt');
      return false;
    }

    setIsLoading(true);
    try {
      const tracked = await trackFeatureUsage(user.id, featureType);
      await loadDailyUsage();
      return tracked;
    } catch (error) {
      console.error(`Error tracking usage for ${featureType}:`, error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user, isPaidUser, loadDailyUsage, signInAnonymously, showCaptchaModal]);

  // Start a live session
  const startPillScanSession = useCallback(async (): Promise<string | null> => {
    // Premium users always have access to live pill scan
    if (isPremiumUser) {
      console.log('Premium user starting live pill scan session');

      if (!user) {
        console.error('No user for premium user check');
        return null;
      }

      setIsLoading(true);
      try {
        const sessionId = await startLiveSession(user.id);
        if (sessionId) {
          await loadActiveSessions();
        } else {
          console.error('Failed to start session for premium user');
        }
        return sessionId;
      } catch (error) {
        console.error('Error starting pill scan session for premium user:', error);
        return null;
      } finally {
        setIsLoading(false);
      }
    }

    // For non-premium users, check feature access
    if (!await checkFeatureAccess(FeatureType.LIVE_PILL_SCAN)) {
      return null;
    }

    // At this point, we should have a user (either existing or anonymous)
    // because checkFeatureAccess would have created an anonymous user if needed
    if (!user) {
      console.error('No user after successful feature access check');
      return null;
    }

    setIsLoading(true);
    try {
      const sessionId = await startLiveSession(user.id);

      if (sessionId) {
        // Track usage for non-paid users
        if (!isPaidUser) {
          await trackUsage(FeatureType.LIVE_PILL_SCAN);
        }

        await loadActiveSessions();
      }

      return sessionId;
    } catch (error) {
      console.error('Error starting pill scan session:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [user, isPaidUser, isPremiumUser, checkFeatureAccess, trackUsage, loadActiveSessions]);

  // End a live session
  const endPillScanSession = useCallback(async (sessionId: string): Promise<boolean> => {
    if (!sessionId) return false;

    setIsLoading(true);
    try {
      const ended = await endLiveSession(sessionId);

      if (ended) {
        setActiveSession(null);
        setSessionTimeRemaining(null);
      }

      return ended;
    } catch (error) {
      console.error('Error ending pill scan session:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get usage count for a feature
  const getUsageCount = useCallback((featureType: FeatureType): number => {
    const usage = dailyUsage.find(u => u.feature_type === featureType);
    return usage?.usage_count || 0;
  }, [dailyUsage]);

  // Get remaining usage for a feature
  const getRemainingUsage = useCallback((featureType: FeatureType): number => {
    // Get the feature limit for the user's tier
    const tierLimits = FEATURE_LIMITS[userTier as keyof typeof FEATURE_LIMITS] || FEATURE_LIMITS.free;
    const featureLimit = tierLimits[featureType];

    // If feature is unlimited or not available
    if (featureLimit === Infinity) return Infinity;
    if (featureLimit === 0) return 0;

    // Calculate remaining usage
    const used = getUsageCount(featureType);
    return Math.max(0, featureLimit - used);
  }, [userTier, getUsageCount]);

  // Update session time remaining
  useEffect(() => {
    if (!activeSession || !activeSession.is_active) {
      setSessionTimeRemaining(null);
      return;
    }

    // Admin and premium users don't have a time limit
    if (isAdminUser || isPremiumUser) {
      setSessionTimeRemaining(null);
      return;
    }

    const sessionStart = new Date(activeSession.session_start).getTime();
    const now = Date.now();
    const elapsedSeconds = Math.floor((now - sessionStart) / 1000);
    const remaining = Math.max(0, LIVE_SESSION_TIME_LIMIT - elapsedSeconds);

    setSessionTimeRemaining(remaining);

    // Set up timer to update remaining time
    const timer = setInterval(() => {
      const now = Date.now();
      const elapsedSeconds = Math.floor((now - sessionStart) / 1000);
      const remaining = Math.max(0, LIVE_SESSION_TIME_LIMIT - elapsedSeconds);

      setSessionTimeRemaining(remaining);

      // End session if time is up
      if (remaining <= 0 && activeSession.id) {
        endPillScanSession(activeSession.id);
        clearInterval(timer);

        // Show time's up message
        Alert.alert(
          t('sessionEndedTitle'),
          t('liveScanTimeLimit'),
          [{ text: t('ok') }]
        );
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [activeSession, isAdminUser, isPremiumUser, endPillScanSession, t]);

  // Load initial data
  useEffect(() => {
    if (user) {
      loadDailyUsage();
      loadActiveSessions();
    } else {
      setDailyUsage([]);
      setActiveSession(null);
    }
  }, [user, loadDailyUsage, loadActiveSessions]);

  return {
    // User tier information
    userTier,
    isPaidUser,
    isProUser,
    isPremiumUser,
    isAdminUser,
    isAnonymous,

    // Loading and session state
    isLoading,
    dailyUsage,
    activeSession,
    sessionTimeRemaining,

    // Feature access functions
    checkFeatureAccess,
    trackUsage,
    startPillScanSession,
    endPillScanSession,
    getUsageCount,
    getRemainingUsage,

    // Constants
    FEATURE_LIMITS,
    LIVE_SESSION_TIME_LIMIT,

    // CAPTCHA related
    isCaptchaModalVisible,
    handleCaptchaVerify,
    closeCaptchaModal
  };
};
