// Supabase Edge Function for canceling Stripe subscriptions
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';
import Stripe from 'https://esm.sh/stripe@12.0.0';

// Initialize Stripe with your secret key
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
});

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
      status: 204,
    });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    // Parse the request body
    const { userId } = await req.json();

    // Validate required parameters
    if (!userId) {
      return new Response('Missing required parameters', { status: 400 });
    }

    // Get the user's subscription ID
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('stripe_subscription_id')
      .eq('id', userId)
      .single();

    if (userError || !user || !user.stripe_subscription_id) {
      return new Response(
        JSON.stringify({ error: 'User not found or no active subscription' }),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
          status: 404,
        }
      );
    }

    // Cancel the subscription
    await stripe.subscriptions.cancel(user.stripe_subscription_id, {
      prorate: true,
    });

    // Update the user's profile
    const { error } = await supabase.rpc('update_subscription_status', {
      user_uuid: userId,
      stripe_subscription_id: null,
      status: 'canceled',
      period_start: null,
      period_end: null,
      subscription_tier: 'free',
    });

    if (error) {
      console.error('Error updating subscription status:', error);
    }

    // Return success
    return new Response(
      JSON.stringify({ success: true }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 200,
      }
    );
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 500,
      }
    );
  }
});
