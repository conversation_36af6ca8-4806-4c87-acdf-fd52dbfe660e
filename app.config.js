import { ExpoConfig, ConfigContext } from 'expo/config';

// Read environment variables from .env file
// In development, this loads from .env
// In production builds, this loads from .env.production if it exists, otherwise from .env
import 'dotenv/config';

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: 'PillLogic',
  slug: 'pilllogic',
  version: '1.0.0',
  orientation: 'portrait',
  icon: './assets/images/icon.png',
  scheme: 'pilllogic',
  userInterfaceStyle: 'automatic',
  splash: {
    image: './assets/images/splash-icon.png',
    resizeMode: 'contain',
    backgroundColor: '#ffffff',
  },
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.pilllogic.app',
    infoPlist: {
      NSCameraUsageDescription: 'This app uses the camera to take photos for image analysis.',
      NSPhotoLibraryUsageDescription: 'This app uses the photo library to select images for analysis.',
      CFBundleURLTypes: [
        {
          CFBundleURLSchemes: ['pilllogic'],
        },
      ],
      // Indicate that the app only uses standard/exempt encryption
      ITSAppUsesNonExemptEncryption: false,
    },
  },
  android: {
    package: 'com.pilllogic.app',
    adaptiveIcon: {
      foregroundImage: './assets/images/adaptive-icon.png',
      backgroundColor: '#ffffff',
    },
    permissions: ['CAMERA', 'READ_EXTERNAL_STORAGE', 'WRITE_EXTERNAL_STORAGE'],
    intentFilters: [
      {
        action: 'VIEW',
        autoVerify: true,
        data: [
          {
            scheme: 'pilllogic',
          },
        ],
        category: ['BROWSABLE', 'DEFAULT'],
      },
    ],
  },
  web: {
    bundler: 'metro',
    output: 'static',
    favicon: './assets/images/favicon.png',
  },
  extra: {
    eas: {
      projectId: 'cd39a25c-35d8-4b98-9c0b-ebb5b247eda8',
    },
    // Add environment variables here
    supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL,
    supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
    resendApiKey: process.env.EXPO_PUBLIC_RESEND_API_KEY,
    // Stripe configuration
    stripePublishableKey: process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY,
    stripePricePro: process.env.EXPO_PUBLIC_STRIPE_PRICE_PRO,
    stripePricePremium: process.env.EXPO_PUBLIC_STRIPE_PRICE_PREMIUM,
    apiUrl: process.env.EXPO_PUBLIC_API_URL,
    // API keys for services
    openaiApiKey: process.env.EXPO_PUBLIC_OPENAI_API_KEY,
    roboflowApiKey: process.env.EXPO_PUBLIC_ROBOFLOW_API_KEY,
    roboflowProjectId: process.env.EXPO_PUBLIC_ROBOFLOW_PROJECT_ID,
    roboflowVersion: process.env.EXPO_PUBLIC_ROBOFLOW_VERSION,
    // DeepSeek configuration
    deepseekApiKey: process.env.EXPO_PUBLIC_DEEPSEEK_API_KEY,
    deepseekBaseUrl: process.env.EXPO_PUBLIC_DEEPSEEK_BASE_URL,
    // hCAPTCHA configuration
    // Using the official hCAPTCHA test site key if no environment variable is provided
    // This key is meant for testing and works on any domain
    hcaptchaSiteKey: process.env.EXPO_PUBLIC_HCAPTCHA_SITE_KEY || 'f3a0d298-4616-4274-a5fc-45ae6796f7da',
    // For debugging purposes - determine environment based on NODE_ENV instead of __DEV__
    isTestEnvironment: process.env.NODE_ENV !== 'production',
  },
  owner: 'chaupham1092',
  plugins: [
    'expo-router',
    'expo-secure-store',
    [
      'expo-camera',
      {
        cameraPermission: 'Allow $(PRODUCT_NAME) to access your camera',
        microphonePermission: 'Allow $(PRODUCT_NAME) to access your microphone',
        recordAudioAndroid: true,
      },
    ],
    'expo-image-picker',
  ],
  experiments: {
    typedRoutes: true,
  },
});
