// Supabase Edge Function to fix invalid Stripe customer IDs
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';
import Stripe from 'https://esm.sh/stripe@12.0.0';

// Initialize Stripe with your secret key
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
});

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    // Verify the JWT token
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      throw new Error('Invalid token');
    }

    const { userId } = await req.json();

    if (!userId) {
      throw new Error('Missing userId parameter');
    }

    // Get the user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, stripe_customer_id')
      .eq('id', userId)
      .single();

    if (profileError || !profile) {
      throw new Error('User not found');
    }

    console.log('Fixing Stripe customer for user:', userId);
    console.log('Current customer ID:', profile.stripe_customer_id);
    console.log('User email:', profile.email);

    let customerId = profile.stripe_customer_id;
    let customerStatus = 'valid';

    // Check if customer ID exists and is valid
    if (customerId) {
      try {
        const customer = await stripe.customers.retrieve(customerId);
        console.log('Existing customer is valid:', customer.id);
        
        // Check if email matches
        if (customer.email !== profile.email) {
          console.log('Email mismatch - updating customer email');
          await stripe.customers.update(customerId, {
            email: profile.email
          });
          customerStatus = 'email_updated';
        }
      } catch (error) {
        console.log('Invalid customer ID, creating new customer');
        customerStatus = 'recreated';
        
        // Create new customer
        const newCustomer = await stripe.customers.create({
          email: profile.email,
          metadata: {
            userId: userId,
            supabase_user_id: userId,
            fixed_at: new Date().toISOString()
          }
        });
        
        customerId = newCustomer.id;
        
        // Update the profile
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ stripe_customer_id: customerId })
          .eq('id', userId);
          
        if (updateError) {
          throw new Error(`Failed to update profile: ${updateError.message}`);
        }
      }
    } else {
      console.log('No customer ID found, creating new customer');
      customerStatus = 'created';
      
      // Create new customer
      const newCustomer = await stripe.customers.create({
        email: profile.email,
        metadata: {
          userId: userId,
          supabase_user_id: userId,
          created_at: new Date().toISOString()
        }
      });
      
      customerId = newCustomer.id;
      
      // Update the profile
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ stripe_customer_id: customerId })
        .eq('id', userId);
        
      if (updateError) {
        throw new Error(`Failed to update profile: ${updateError.message}`);
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        customerId,
        status: customerStatus,
        email: profile.email
      }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 200,
      }
    );
  } catch (error) {
    console.error('Error fixing Stripe customer:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 500,
      }
    );
  }
});
