import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Alert, ActivityIndicator, Platform } from 'react-native';
import { Text, Container, Header, But<PERSON>, Divider } from '../components/ui';
import { Colors, Spacing } from '../constants/DesignSystem';
import { useLanguage } from '../contexts/LanguageContext';
import { useUsageLimits } from '../hooks/useUsageLimits';
import { useAuth } from '../contexts/AuthContext';
import { FeatureType } from '../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import {
  initializeIAP,
  getProducts,
  purchaseSubscription,
  restorePurchases,
  checkSubscriptionStatus,
  IAP_PRODUCT_IDS,
  IAP_PRODUCTS,
  formatPrice
} from '../services/appleIAPService';
import * as InAppPurchases from 'expo-in-app-purchases';

export default function SubscriptionScreen() {
  const { t } = useLanguage();
  const { userTier, FEATURE_LIMITS } = useUsageLimits();
  const { user, refreshUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [products, setProducts] = useState<InAppPurchases.IAPItemDetails[]>([]);
  const [iapInitialized, setIapInitialized] = useState(false);

  // Initialize IAP and load products when component mounts
  useEffect(() => {
    const initializeAndLoadProducts = async () => {
      if (Platform.OS !== 'ios') {
        console.log('Not on iOS, skipping IAP initialization');
        return;
      }

      try {
        console.log('Initializing Apple In-App Purchases...');
        const initialized = await initializeIAP();
        setIapInitialized(initialized);

        if (initialized) {
          console.log('Loading products from App Store...');
          const availableProducts = await getProducts();
          setProducts(availableProducts);
          console.log('Loaded', availableProducts.length, 'products');
        }
      } catch (error) {
        console.error('Error initializing IAP:', error);
      }
    };

    initializeAndLoadProducts();
  }, []);

  // Check subscription status when component mounts
  useEffect(() => {
    if (user) {
      console.log('SubscriptionScreen mounted, current user tier:', user.subscription_tier);

      // If we're coming from a subscription process, check status
      const checkStatus = async () => {
        try {
          console.log('Checking subscription status on mount...');
          const status = await checkSubscriptionStatus(user.id);
          console.log('Subscription check response on mount:', status);

          if (status.subscription_tier && status.subscription_tier !== 'free' &&
              status.subscription_tier !== user.subscription_tier) {
            console.log('Subscription tier mismatch, refreshing user data...');
            await refreshUser();
          }
        } catch (error) {
          console.error('Error checking subscription status on mount:', error);
        }
      };

      checkStatus();
    }
  }, [user]);

  // Function to manually refresh subscription status
  const handleRefreshSubscription = async () => {
    if (!user) {
      Alert.alert(
        t('loginRequired'),
        t('loginRequiredMessage'),
        [{ text: t('ok') }]
      );
      return;
    }

    try {
      setIsRefreshing(true);
      console.log('Manually refreshing subscription status...');
      console.log('Current user tier:', user.subscription_tier);

      // Check subscription status
      const status = await checkSubscriptionStatus(user.id);
      console.log('Subscription check response:', status);

      // Refresh user data
      await refreshUser();
      console.log('User data refreshed, new tier:', user.subscription_tier);

      // Show success message
      Alert.alert(
        t('refreshComplete'),
        t('subscriptionStatusRefreshed'),
        [{ text: t('ok') }]
      );
    } catch (error) {
      console.error('Error refreshing subscription status:', error);
      Alert.alert(
        t('errorTitle'),
        t('refreshError'),
        [{ text: t('ok') }]
      );
    } finally {
      setIsRefreshing(false);
    }
  };

  // Function to display feature limit
  const getFeatureLimit = (tier: string, featureType: FeatureType) => {
    const tierLimits = FEATURE_LIMITS[tier as keyof typeof FEATURE_LIMITS];
    // Use type assertion to fix TypeScript error
    const limit = tierLimits[featureType as keyof typeof tierLimits];

    if (limit === Infinity) {
      return t('unlimited');
    } else if (limit === 0) {
      return t('notAvailable');
    } else if (featureType === FeatureType.LIVE_PILL_SCAN && tier === 'pro') {
      return "5-/day";
    } else {
      return t('perDay', { count: limit });
    }
  };

  // Function to handle subscription
  const handleSubscribe = async (tier: string) => {
    if (!user) {
      Alert.alert(
        t('loginRequired'),
        t('loginRequiredMessage'),
        [{ text: t('ok') }]
      );
      return;
    }

    if (Platform.OS !== 'ios') {
      Alert.alert(
        'Not Available',
        'In-App Purchases are only available on iOS devices.',
        [{ text: t('ok') }]
      );
      return;
    }

    if (!iapInitialized) {
      Alert.alert(
        t('errorTitle'),
        'In-App Purchases are not available. Please try again later.',
        [{ text: t('ok') }]
      );
      return;
    }

    try {
      setIsLoading(true);

      // Get the product ID based on the tier
      const productId = tier === 'pro' ? IAP_PRODUCT_IDS.PRO_MONTHLY : IAP_PRODUCT_IDS.PREMIUM_MONTHLY;

      // Make the purchase
      const result = await purchaseSubscription(productId, user.id);

      if (result.success) {
        Alert.alert(
          'Success!',
          result.message,
          [
            {
              text: t('ok'),
              onPress: async () => {
                // Refresh user data to reflect new subscription
                await refreshUser();
              }
            }
          ]
        );
      } else {
        Alert.alert(
          t('errorTitle'),
          result.message,
          [{ text: t('ok') }]
        );
      }
    } catch (error) {
      console.error('Error subscribing:', error);
      Alert.alert(
        t('errorTitle'),
        'An error occurred during purchase. Please try again.',
        [{ text: t('ok') }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fix Stripe customer issues
  const handleFixStripeCustomer = async () => {
    if (!user) {
      Alert.alert(
        t('loginRequired'),
        t('loginRequiredMessage'),
        [{ text: t('ok') }]
      );
      return;
    }

    try {
      setIsLoading(true);
      console.log('Fixing Stripe customer for user:', user.id);

      const result = await fixStripeCustomer(user.id);

      if (result.success) {
        Alert.alert(
          'Customer Fixed',
          `Your Stripe customer has been ${result.status}. New customer ID: ${result.customerId}`,
          [
            {
              text: 'Try Portal Again',
              onPress: handleManageSubscription
            },
            {
              text: 'OK'
            }
          ]
        );
      } else {
        Alert.alert(
          'Fix Failed',
          result.error || 'Failed to fix Stripe customer',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error fixing Stripe customer:', error);
      Alert.alert(
        'Error',
        'An unexpected error occurred while fixing your customer record',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle restoring purchases
  const handleRestorePurchases = async () => {
    if (!user) {
      Alert.alert(
        t('loginRequired'),
        t('loginRequiredMessage'),
        [{ text: t('ok') }]
      );
      return;
    }

    if (Platform.OS !== 'ios') {
      Alert.alert(
        'Not Available',
        'Purchase restoration is only available on iOS devices.',
        [{ text: t('ok') }]
      );
      return;
    }

    try {
      setIsLoading(true);
      const result = await restorePurchases(user.id);

      Alert.alert(
        result.success ? 'Success!' : 'Restore Failed',
        result.message,
        [
          {
            text: t('ok'),
            onPress: async () => {
              if (result.success) {
                await refreshUser();
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error restoring purchases:', error);
      Alert.alert(
        t('errorTitle'),
        'Failed to restore purchases. Please try again.',
        [{ text: t('ok') }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle managing subscription (Apple IAP)
  const handleManageSubscription = async () => {
    if (!user) {
      Alert.alert(
        t('loginRequired'),
        t('loginRequiredMessage'),
        [{ text: t('ok') }]
      );
      return;
    }

    // For Apple IAP, subscription management is handled through iOS Settings
    Alert.alert(
      'Manage Subscription',
      'To manage your subscription, go to:\n\nSettings > Apple ID > Subscriptions > PillLogic\n\nYou can cancel, modify, or view your subscription details there.',
      [
        {
          text: 'Restore Purchases',
          onPress: handleRestorePurchases
        },
        {
          text: 'Refresh Status',
          onPress: handleRefreshSubscription
        },
        {
          text: t('ok')
        }
      ]
    );
  };

  // Render a table cell with checkmark, X, or text
  const renderTableCell = (tier: string, featureType: FeatureType) => {
    const tierLimits = FEATURE_LIMITS[tier as keyof typeof FEATURE_LIMITS];
    // Use type assertion to fix TypeScript error
    const limit = tierLimits[featureType as keyof typeof tierLimits];

    if (limit === 0) {
      return (
        <View style={[styles.tableCell, tier === 'premium' && styles.premiumCell]}>
          <Ionicons name="close" size={24} color={Colors.error} />
        </View>
      );
    } else if (limit === Infinity) {
      return (
        <View style={[styles.tableCell, tier === 'premium' && styles.premiumCell]}>
          <Ionicons name="checkmark" size={24} color={Colors.success} />
        </View>
      );
    } else {
      let displayText = "";

      if (featureType === FeatureType.PILL_SCAN) {
        // For free tier, show "10-/day" instead of checkmark
        if (tier === 'free' && limit === 10) {
          displayText = "10-/day";
        } else {
          displayText = `${limit}/day`;
        }
      } else if (featureType === FeatureType.LIVE_PILL_SCAN) {
        displayText = tier === 'pro' ? "5-/day" : "✓";
      } else if (featureType === FeatureType.NOTE_ANALYSIS) {
        // For free tier, show "10-/day" instead of checkmark
        if (tier === 'free' && limit === 10) {
          displayText = "10-/day";
        } else {
          displayText = `${limit}/day`;
        }
      }

      return (
        <View style={[styles.tableCell, tier === 'premium' && styles.premiumCell]}>
          {displayText === "✓" ? (
            <Ionicons name="checkmark" size={24} color={Colors.success} />
          ) : (
            <Text variant="body" weight="medium">{displayText}</Text>
          )}
        </View>
      );
    }
  };

  return (
    <Container>
      <Header title={t('subscriptionTitle')} showBackButton />

      <ScrollView style={styles.scrollContent} contentContainerStyle={styles.contentContainer}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <Text variant="h2" weight="bold" style={styles.mainTitle}>
            Stay on top of your meds – your way
          </Text>
          <Text variant="body" style={styles.subtitle}>
            {userTier === 'free' ? "Choose the plan that works for you:" : "Here's what's included in each plan:"}
          </Text>
        </View>

        {/* Plan Table */}
        <View style={styles.tableContainer}>
          {/* Table Header */}
          <View style={styles.tableRow}>
            <View style={styles.featureNameCell}></View>
            <View style={styles.tableCell}>
              <Text variant="subtitle" weight="semibold">Free</Text>
            </View>
            <View style={styles.tableCell}>
              <Text variant="subtitle" weight="semibold">Pro</Text>
            </View>
            <View style={[styles.tableCell, styles.premiumCell]}>
              <Text variant="subtitle" weight="semibold">Premium</Text>
            </View>
          </View>

          {/* Divider */}
          <View style={styles.tableDivider} />

          {/* Feature Rows */}
          <View style={styles.tableRow}>
            <View style={styles.featureNameCell}>
              <Text variant="body" weight="medium">Pill Count</Text>
            </View>
            {renderTableCell('free', FeatureType.PILL_SCAN)}
            {renderTableCell('pro', FeatureType.PILL_SCAN)}
            {renderTableCell('premium', FeatureType.PILL_SCAN)}
          </View>

          <View style={styles.tableDivider} />

          <View style={styles.tableRow}>
            <View style={styles.featureNameCell}>
              <Text variant="body" weight="medium">Live Count (Video)</Text>
            </View>
            {renderTableCell('free', FeatureType.LIVE_PILL_SCAN)}
            {renderTableCell('pro', FeatureType.LIVE_PILL_SCAN)}
            {renderTableCell('premium', FeatureType.LIVE_PILL_SCAN)}
          </View>

          <View style={styles.tableDivider} />

          <View style={styles.tableRow}>
            <View style={styles.featureNameCell}>
              <Text variant="body" weight="medium">Doctor Note Analyzing</Text>
            </View>
            {renderTableCell('free', FeatureType.NOTE_ANALYSIS)}
            {renderTableCell('pro', FeatureType.NOTE_ANALYSIS)}
            {renderTableCell('premium', FeatureType.NOTE_ANALYSIS)}
          </View>

          <View style={styles.tableDivider} />

          <View style={styles.tableRow}>
            <View style={styles.featureNameCell}>
              <Text variant="body" weight="medium">AI Updates & Support</Text>
            </View>
            <View style={styles.tableCell}>
              <Ionicons name="checkmark" size={24} color={Colors.success} />
            </View>
            <View style={styles.tableCell}>
              <Ionicons name="checkmark" size={24} color={Colors.success} />
            </View>
            <View style={[styles.tableCell, styles.premiumCell]}>
              <Ionicons name="checkmark" size={24} color={Colors.success} />
            </View>
          </View>
        </View>

        {/* Plan Details */}
        <Text variant="h3" weight="semibold" style={styles.choosePlanTitle}>
          Choose your plan:
        </Text>

        <View style={styles.planCardsContainer}>
          {/* Free Plan */}
          <View style={styles.planCard}>
            <Text variant="subtitle" weight="semibold">Free – $0</Text>
            <Text variant="body" color="secondary" style={styles.planDescription}>
              Stick with standard pill tracking. Basic and reliable.
            </Text>
            {userTier === 'free' && (
              <View style={styles.currentPlanTag}>
                <Text variant="caption" color="white">Current Plan</Text>
              </View>
            )}
          </View>

          {/* Pro Plan */}
          <View style={styles.planCard}>
            <Text variant="subtitle" weight="semibold">
              Pro – {(() => {
                const proProduct = products.find(p => p.productId === IAP_PRODUCT_IDS.PRO_MONTHLY);
                return proProduct ? formatPrice(proProduct) : '$2.99/month';
              })()}
            </Text>
            <Text variant="body" color="secondary" style={styles.planDescription}>
              Try limited live pill count. No note analysis.
            </Text>
            {userTier === 'pro' && (
              <View style={styles.currentPlanTag}>
                <Text variant="caption" color="white">Current Plan</Text>
              </View>
            )}
          </View>

          {/* Premium Plan */}
          <View style={[styles.planCard, styles.premiumPlanCard]}>
            <Text variant="subtitle" weight="semibold">
              Premium – {(() => {
                const premiumProduct = products.find(p => p.productId === IAP_PRODUCT_IDS.PREMIUM_MONTHLY);
                return premiumProduct ? formatPrice(premiumProduct) : '$5.99/month';
              })()}
            </Text>
            <Text variant="body" color="secondary" style={styles.planDescription}>
              Everything unlocked. Unlimited counts. Doctor note analysis. Always updated.
            </Text>
            {userTier === 'premium' && (
              <View style={styles.currentPlanTag}>
                <Text variant="caption" color="white">Current Plan</Text>
              </View>
            )}
          </View>
        </View>

        {/* CTA Section */}
        <View style={styles.ctaSection}>
          <Text variant="subtitle" weight="semibold" style={styles.ctaText}>
            Already using the app? Keep going without losing your flow.
          </Text>

          {userTier === 'free' ? (
            <View style={styles.buttonContainer}>
              <Button
                title={isLoading ? t('processing') : "Get Pro Plan"}
                variant="secondary"
                size="lg"
                style={styles.proButton}
                onPress={() => handleSubscribe('pro')}
                disabled={isLoading}
              >
                {isLoading && (
                  <ActivityIndicator size="small" color={Colors.primary} style={styles.buttonLoader} />
                )}
              </Button>

              <Button
                title={isLoading ? t('processing') : "Get Premium Plan"}
                variant="primary"
                size="lg"
                style={styles.premiumButton}
                onPress={() => handleSubscribe('premium')}
                disabled={isLoading}
              >
                {isLoading && (
                  <ActivityIndicator size="small" color="white" style={styles.buttonLoader} />
                )}
              </Button>
            </View>
          ) : (
            <>
              <Button
                title={isLoading ? t('processing') : t('manageSubscription')}
                variant="secondary"
                size="lg"
                style={styles.manageButton}
                onPress={handleManageSubscription}
                disabled={isLoading}
              >
                {isLoading && (
                  <ActivityIndicator size="small" color={Colors.primary} style={styles.buttonLoader} />
                )}
              </Button>

              <TouchableOpacity
                style={styles.refreshButton}
                onPress={handleRefreshSubscription}
                disabled={isRefreshing}
              >
                <Text style={styles.refreshButtonText}>
                  {isRefreshing ? 'Refreshing...' : 'Refresh Subscription Status'}
                </Text>
                {isRefreshing && (
                  <ActivityIndicator size="small" color={Colors.primary} style={styles.buttonLoader} />
                )}
              </TouchableOpacity>
              <Text style={styles.refreshHint}>
                If your subscription status is not showing correctly, try refreshing.
              </Text>
            </>
          )}
        </View>
      </ScrollView>
    </Container>
  );
}

const styles = StyleSheet.create({
  scrollContent: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: Spacing.xl,
    paddingTop: Spacing.md,
    paddingHorizontal: Spacing.md,
    backgroundColor: '#FFFAF5', // Light cream background like in the inspiration
  },
  headerContainer: {
    marginBottom: Spacing.lg,
    paddingTop: Spacing.md,
  },
  mainTitle: {
    fontSize: 28,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: Spacing.lg,
    color: Colors.textSecondary,
  },
  tableContainer: {
    marginBottom: Spacing.xl,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  tableRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tableDivider: {
    height: 1,
    backgroundColor: '#E5E5E5',
    width: '100%',
  },
  featureNameCell: {
    flex: 1.5,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    justifyContent: 'center',
  },
  tableCell: {
    flex: 1,
    paddingVertical: Spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  premiumCell: {
    backgroundColor: '#EEEAF9', // Light purple for premium column
  },
  choosePlanTitle: {
    marginBottom: Spacing.md,
    marginTop: Spacing.lg,
  },
  planCardsContainer: {
    marginBottom: Spacing.xl,
  },
  planCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: 'white',
    position: 'relative',
  },
  premiumPlanCard: {
    backgroundColor: '#EEEAF9', // Light purple for premium
  },
  planDescription: {
    marginTop: Spacing.xs,
  },
  currentPlanTag: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
    backgroundColor: Colors.success,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: 12,
  },
  ctaSection: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  ctaText: {
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  buttonContainer: {
    width: '100%',
    gap: Spacing.md,
  },
  proButton: {
    width: '100%',
    borderRadius: 25,
    borderColor: Colors.primary,
    paddingVertical: Spacing.md,
  },
  premiumButton: {
    width: '100%',
    borderRadius: 25,
    backgroundColor: '#6C5CE7', // Purple button like in the inspiration
    paddingVertical: Spacing.md,
  },
  buttonLoader: {
    marginLeft: Spacing.sm,
  },
  manageButton: {
    width: '100%',
    borderRadius: 25,
    borderColor: Colors.primary,
    paddingVertical: Spacing.md,
  },
  refreshButton: {
    marginTop: Spacing.md,
    padding: Spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  refreshButtonText: {
    color: Colors.primary,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  refreshHint: {
    marginTop: Spacing.xs,
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});
