{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slug": "pilllogic", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "pilllogic", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.pilllogic.app", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to take photos for image analysis.", "NSPhotoLibraryUsageDescription": "This app uses the photo library to select images for analysis.", "CFBundleURLTypes": [{"CFBundleURLSchemes": ["pilllogic"]}]}}, "android": {"package": "com.pilllogic.app", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"], "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "pilllogic"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "extra": {"eas": {"projectId": "docaid"}}, "owner": "chaupham1092", "plugins": ["expo-router", "expo-secure-store", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], "expo-image-picker"], "experiments": {"typedRoutes": true}}}