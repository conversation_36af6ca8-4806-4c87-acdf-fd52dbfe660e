// Supabase Edge Function for handling Stripe webhooks without signature verification
// This is a temporary solution until we can fix the webhook signature verification
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';
import Stripe from 'https://esm.sh/stripe@12.0.0';

// Define Deno namespace for TypeScript
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// Initialize Stripe with your secret key
const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY') || '';
console.log(`STRIPE_SECRET_KEY: ${stripeSecretKey ? 'present (starts with ' + stripeSecretKey.substring(0, 3) + '...)' : 'missing'}`);

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2023-10-16',
});

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
console.log(`SUPABASE_URL: ${supabaseUrl ? 'present' : 'missing'}`);

const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
console.log(`SUPABASE_SERVICE_ROLE_KEY: ${supabaseServiceKey ? 'present (starts with ' + supabaseServiceKey.substring(0, 3) + '...)' : 'missing'}`);

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Map Stripe product IDs to subscription tiers
const PRODUCT_TO_TIER: Record<string, string> = {
  // Live mode product IDs
  'prod_SH3bdnxoqv9qZj': 'pro',
  'prod_SH3cwfScCokdPO': 'premium',
  // Test mode product IDs
  'prod_SH5FpKgq7oA3jp': 'pro',
  'prod_SH5GbiJpNVKzOe': 'premium',
};

serve(async (req: Request) => {
  console.log('Webhook received a request');
  
  // Log all headers for debugging
  console.log('Request headers:');
  for (const [key, value] of req.headers.entries()) {
    console.log(`${key}: ${value}`);
  }

  try {
    // Get the raw body
    const body = await req.text();
    console.log(`Request body length: ${body.length}`);
    console.log(`Request body preview: ${body.substring(0, 100)}...`);

    // Parse the body as JSON
    try {
      const jsonBody = JSON.parse(body);
      console.log(`Event type from body: ${jsonBody.type}`);
      
      // Handle the event based on type
      if (jsonBody.type === 'checkout.session.completed') {
        await handleCheckoutSessionCompleted(jsonBody.data.object);
      } else if (jsonBody.type === 'customer.subscription.updated') {
        await handleSubscriptionUpdated(jsonBody.data.object);
      } else if (jsonBody.type === 'customer.subscription.deleted') {
        await handleSubscriptionDeleted(jsonBody.data.object);
      } else {
        console.log(`Unhandled event type: ${jsonBody.type}`);
      }
      
      return new Response(JSON.stringify({ received: true }), {
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 200,
      });
    } catch (e) {
      console.error('Error parsing webhook body as JSON:', e);
      return new Response('Invalid payload', { 
        headers: { 'Access-Control-Allow-Origin': '*' },
        status: 400 
      });
    }
  } catch (err: any) {
    console.error(`Error processing webhook: ${err.message}`);
    console.error('Full error:', err);
    return new Response(`Webhook Error: ${err.message}`, { 
      headers: { 'Access-Control-Allow-Origin': '*' },
      status: 400 
    });
  }
});

// Handle checkout.session.completed event
async function handleCheckoutSessionCompleted(session: any) {
  // Get the customer and subscription IDs
  const customerId = session.customer;
  const subscriptionId = session.subscription;

  if (!customerId || !subscriptionId) {
    console.error('Missing customer or subscription ID');
    return;
  }

  try {
    // Get the subscription details
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    // Get the product ID from the subscription
    const productId = subscription.items.data[0].price.product;

    // Get the user ID from the metadata
    const userId = session.client_reference_id;

    if (!userId) {
      console.error('Missing user ID in session metadata');
      return;
    }

    // Map the product ID to a subscription tier
    const tier = PRODUCT_TO_TIER[productId] || 'free';

    console.log(`Webhook: Processing subscription for user ${userId}`);
    console.log(`Webhook: Product ID: ${productId}`);
    console.log(`Webhook: Mapped to tier: ${tier}`);
    console.log(`Webhook: Subscription status: ${subscription.status}`);

    // Update the user's profile
    const { error } = await supabase.rpc('update_subscription_status', {
      user_uuid: userId,
      stripe_subscription_id: subscriptionId,
      status: subscription.status,
      period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      subscription_tier: tier,
    });

    if (error) {
      console.error('Error updating subscription status:', error);
      console.error('Error details:', JSON.stringify(error));
    } else {
      console.log(`Webhook: Successfully updated subscription for user ${userId} to tier ${tier}`);

      // Double-check the update by querying the profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('subscription_tier')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error('Error verifying profile update:', profileError);
      } else {
        console.log(`Webhook: Verified profile tier is now: ${profile.subscription_tier}`);
      }
    }
  } catch (error) {
    console.error('Error handling checkout.session.completed:', error);
  }
}

// Handle customer.subscription.updated event
async function handleSubscriptionUpdated(subscription: any) {
  try {
    // Get the customer ID
    const customerId = subscription.customer;

    if (!customerId) {
      console.error('Missing customer ID');
      return;
    }

    // Get the user with this Stripe customer ID
    const { data: users, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .eq('stripe_customer_id', customerId);

    if (userError || !users || users.length === 0) {
      console.error('Error getting user with Stripe customer ID:', userError);
      return;
    }

    const userId = users[0].id;

    // Get the product ID from the subscription
    const productId = subscription.items.data[0].price.product;

    // Map the product ID to a subscription tier
    const tier = PRODUCT_TO_TIER[productId] || 'free';

    // Update the user's profile
    const { error } = await supabase.rpc('update_subscription_status', {
      user_uuid: userId,
      stripe_subscription_id: subscription.id,
      status: subscription.status,
      period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      subscription_tier: tier,
    });

    if (error) {
      console.error('Error updating subscription status:', error);
    }
  } catch (error) {
    console.error('Error handling customer.subscription.updated:', error);
  }
}

// Handle customer.subscription.deleted event
async function handleSubscriptionDeleted(subscription: any) {
  try {
    // Get the customer ID
    const customerId = subscription.customer;

    if (!customerId) {
      console.error('Missing customer ID');
      return;
    }

    // Get the user with this Stripe customer ID
    const { data: users, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .eq('stripe_customer_id', customerId);

    if (userError || !users || users.length === 0) {
      console.error('Error getting user with Stripe customer ID:', userError);
      return;
    }

    const userId = users[0].id;

    // Update the user's profile
    const { error } = await supabase.rpc('update_subscription_status', {
      user_uuid: userId,
      stripe_subscription_id: null,
      status: 'canceled',
      period_start: null,
      period_end: null,
      subscription_tier: 'free',
    });

    if (error) {
      console.error('Error updating subscription status:', error);
    }
  } catch (error) {
    console.error('Error handling customer.subscription.deleted:', error);
  }
}
