// Debug function to check Stripe customers
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';
import Stripe from 'https://esm.sh/stripe@12.0.0';

// Initialize Stripe with your secret key
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
});

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  try {
    const { email, customerId } = await req.json();

    const results = {
      searchEmail: email || null,
      searchCustomerId: customerId || null,
      stripeMode: Deno.env.get('STRIPE_SECRET_KEY')?.startsWith('sk_test_') ? 'TEST' : 'LIVE',
      customerByEmail: null,
      customerById: null,
      allCustomers: [],
      error: null
    };

    // Search by email if provided
    if (email) {
      try {
        const customers = await stripe.customers.list({
          email: email,
          limit: 10
        });
        results.customerByEmail = customers.data;
        console.log(`Found ${customers.data.length} customers with email ${email}`);
      } catch (error) {
        results.error = `Error searching by email: ${error.message}`;
      }
    }

    // Search by customer ID if provided
    if (customerId) {
      try {
        const customer = await stripe.customers.retrieve(customerId);
        results.customerById = customer;
        console.log(`Found customer by ID: ${customer.id}`);
      } catch (error) {
        results.error = `Error searching by ID: ${error.message}`;
        console.log(`Customer ID ${customerId} not found: ${error.message}`);
      }
    }

    // Get recent customers for reference
    try {
      const recentCustomers = await stripe.customers.list({
        limit: 10
      });
      results.allCustomers = recentCustomers.data.map(customer => ({
        id: customer.id,
        email: customer.email,
        created: new Date(customer.created * 1000).toISOString(),
        metadata: customer.metadata
      }));
    } catch (error) {
      console.log(`Error getting recent customers: ${error.message}`);
    }

    return new Response(
      JSON.stringify(results, null, 2),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 200,
      }
    );
  } catch (error) {
    console.error('Error in debug function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 500,
      }
    );
  }
});
