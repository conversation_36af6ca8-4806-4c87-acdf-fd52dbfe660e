// Supabase Edge Function for checking subscription status
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';
import Stripe from 'https://esm.sh/stripe@12.0.0';

// Define Deno namespace for TypeScript
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// Initialize Stripe with your secret key
const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY') || '';
console.log(`STRIPE_SECRET_KEY: ${stripeSecretKey ? 'present' : 'missing'}`);

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2023-10-16',
});

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Map Stripe product IDs to subscription tiers
const PRODUCT_TO_TIER: Record<string, string> = {
  // Live mode product IDs
  'prod_SH3bdnxoqv9qZj': 'pro',
  'prod_SH3cwfScCokdPO': 'premium',
  // Test mode product IDs
  'prod_SH5FpKgq7oA3jp': 'pro',
  'prod_SH5GbiJpNVKzOe': 'premium',
};

serve(async (req: Request) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
      status: 204,
    });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    // Parse the request body
    const { userId, sessionId } = await req.json();

    // Validate required parameters
    if (!userId) {
      return new Response('Missing required parameters', { status: 400 });
    }

    console.log(`Checking subscription status for user ${userId}`);

    // Get the user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('stripe_customer_id, stripe_subscription_id, subscription_tier')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Error getting user profile:', profileError);
      return new Response(
        JSON.stringify({ error: 'Error getting user profile' }),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
          status: 500,
        }
      );
    }

    console.log(`User profile: ${JSON.stringify(profile)}`);

    // If the user already has a subscription tier other than 'free', return it
    if (profile.subscription_tier && profile.subscription_tier !== 'free') {
      return new Response(
        JSON.stringify({ 
          subscription_tier: profile.subscription_tier,
          status: 'active',
          already_subscribed: true
        }),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
          status: 200,
        }
      );
    }

    // If we have a session ID, check if it was completed
    if (sessionId) {
      try {
        const session = await stripe.checkout.sessions.retrieve(sessionId);
        
        if (session.status === 'complete' && session.subscription) {
          // Get the subscription details
          const subscription = await stripe.subscriptions.retrieve(session.subscription as string);
          
          // Get the product ID from the subscription
          const productId = subscription.items.data[0].price.product;
          
          // Map the product ID to a subscription tier
          const tier = PRODUCT_TO_TIER[productId as string] || 'free';
          
          console.log(`Session ${sessionId} is complete with subscription ${session.subscription}`);
          console.log(`Product ID: ${productId}, mapped to tier: ${tier}`);
          
          // Update the user's profile
          const { error } = await supabase.rpc('update_subscription_status', {
            user_uuid: userId,
            stripe_subscription_id: session.subscription,
            status: subscription.status,
            period_start: new Date(subscription.current_period_start * 1000).toISOString(),
            period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            subscription_tier: tier,
          });
          
          if (error) {
            console.error('Error updating subscription status:', error);
            return new Response(
              JSON.stringify({ error: 'Error updating subscription status' }),
              {
                headers: {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': '*',
                },
                status: 500,
              }
            );
          }
          
          return new Response(
            JSON.stringify({ 
              subscription_tier: tier,
              status: subscription.status,
              manually_updated: true
            }),
            {
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
              },
              status: 200,
            }
          );
        }
      } catch (error: any) {
        console.error(`Error checking session status: ${error.message}`);
      }
    }

    // If the user has a Stripe customer ID but no subscription tier, check for active subscriptions
    if (profile.stripe_customer_id) {
      try {
        // List all subscriptions for this customer
        const subscriptions = await stripe.subscriptions.list({
          customer: profile.stripe_customer_id,
          status: 'active',
          limit: 1,
        });

        if (subscriptions.data.length > 0) {
          const subscription = subscriptions.data[0];
          
          // Get the product ID from the subscription
          const productId = subscription.items.data[0].price.product;
          
          // Map the product ID to a subscription tier
          const tier = PRODUCT_TO_TIER[productId as string] || 'free';
          
          console.log(`Found active subscription ${subscription.id} for customer ${profile.stripe_customer_id}`);
          console.log(`Product ID: ${productId}, mapped to tier: ${tier}`);
          
          // Update the user's profile
          const { error } = await supabase.rpc('update_subscription_status', {
            user_uuid: userId,
            stripe_subscription_id: subscription.id,
            status: subscription.status,
            period_start: new Date(subscription.current_period_start * 1000).toISOString(),
            period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            subscription_tier: tier,
          });
          
          if (error) {
            console.error('Error updating subscription status:', error);
            return new Response(
              JSON.stringify({ error: 'Error updating subscription status' }),
              {
                headers: {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': '*',
                },
                status: 500,
              }
            );
          }
          
          return new Response(
            JSON.stringify({ 
              subscription_tier: tier,
              status: subscription.status,
              manually_updated: true
            }),
            {
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
              },
              status: 200,
            }
          );
        }
      } catch (error: any) {
        console.error(`Error checking customer subscriptions: ${error.message}`);
      }
    }

    // If we get here, the user doesn't have an active subscription
    return new Response(
      JSON.stringify({ 
        subscription_tier: 'free',
        status: 'none',
        manually_checked: true
      }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 200,
      }
    );
  } catch (error: any) {
    console.error('Error checking subscription status:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 500,
      }
    );
  }
});
