import { supabase } from '../lib/supabase';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import * as WebBrowser from 'expo-web-browser';
import { makeRedirectUri } from 'expo-auth-session';

// Stripe configuration from environment variables
const STRIPE_PUBLISHABLE_KEY = Constants.expoConfig?.extra?.stripePublishableKey ||
                              process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY ||
                              '';
const API_URL = Constants.expoConfig?.extra?.apiUrl ||
               process.env.EXPO_PUBLIC_API_URL ||
               '';

// Price IDs for subscription plans
// Replace these with your actual Stripe price IDs
export const STRIPE_PRICES = {
  // Using test mode price IDs for development
  PRO: Constants.expoConfig?.extra?.stripePricePro || process.env.EXPO_PUBLIC_STRIPE_PRICE_PRO || 'price_1RMX53FZGrSXwC9AtnQ6VWFO',
  PREMIUM: Constants.expoConfig?.extra?.stripePricePremium || process.env.EXPO_PUBLIC_STRIPE_PRICE_PREMIUM || 'price_1RMX6PFZGrSXwC9AoBXN4gKI',
};

// Get the redirect URL for Stripe Checkout
export const getRedirectUrl = () => {
  // Use different redirect URLs for different platforms
  if (Platform.OS === 'web') {
    return window.location.origin + '/stripe-redirect';
  } else {
    // For mobile, use a URL that Stripe will accept
    // In development, Expo uses exp:// URLs which Stripe may not accept
    // For testing, we'll use a fallback URL that Stripe will accept
    try {
      const deepLink = makeRedirectUri({
        scheme: 'pilllogic', // Replace with your app's scheme
        path: 'stripe-redirect',
      });

      // Check if the URL is in the exp:// format (development)
      if (deepLink.startsWith('exp://')) {
        console.log('Using fallback URL for Stripe in development');
        // Use a fallback URL that Stripe will accept
        return 'https://pilllogic.com/stripe-redirect';
      }

      return deepLink;
    } catch (error) {
      console.error('Error creating redirect URL:', error);
      // Fallback to a URL that Stripe will definitely accept
      return 'https://pilllogic.com/stripe-redirect';
    }
  }
};

/**
 * Create a Stripe Checkout session for subscription
 * @param userId The user's ID
 * @param email The user's email
 * @param priceId The Stripe price ID for the subscription
 * @returns The Stripe Checkout URL
 */
export const createCheckoutSession = async (
  userId: string,
  email: string,
  priceId: string
): Promise<string | null> => {
  try {
    if (!userId || !email || !priceId) {
      console.error('Missing required parameters for checkout session');
      return null;
    }

    // Get or create Stripe customer ID
    const { data: customerId, error: customerError } = await supabase.rpc(
      'get_or_create_stripe_customer',
      {
        user_uuid: userId,
        email: email
      }
    );

    if (customerError) {
      console.error('Error getting Stripe customer:', customerError);
      return null;
    }

    // Create checkout session via API
    console.log('Calling API at:', `${API_URL}/create-checkout-session`);
    console.log('Request body:', {
      priceId,
      customerId,
      userId,
      email,
      redirectUrl: getRedirectUrl()
    });

    const response = await fetch(`${API_URL}/create-checkout-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
      },
      body: JSON.stringify({
        priceId,
        customerId,
        userId,
        email,
        redirectUrl: getRedirectUrl()
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const responseText = await response.text();
      console.error('Error response text:', responseText);
      try {
        const errorData = JSON.parse(responseText);
        console.error('Error creating checkout session:', errorData);
      } catch (parseError) {
        console.error('Error parsing error response:', parseError);
      }
      return null;
    }

    const responseText = await response.text();
    console.log('Response text:', responseText);

    try {
      const { checkoutUrl } = JSON.parse(responseText);
      return checkoutUrl;
    } catch (parseError) {
      console.error('Error parsing success response:', parseError);
      return null;
    }
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return null;
  }
};

/**
 * Open Stripe Checkout in a browser
 * @param checkoutUrl The Stripe Checkout URL
 * @returns True if the browser was opened successfully
 */
export const openCheckout = async (checkoutUrl: string): Promise<boolean> => {
  try {
    if (!checkoutUrl) {
      console.error('No checkout URL provided');
      return false;
    }

    // Open the checkout URL in a browser
    const result = await WebBrowser.openAuthSessionAsync(checkoutUrl, getRedirectUrl());

    // Check if the browser was opened successfully
    return result.type === 'success';
  } catch (error) {
    console.error('Error opening checkout:', error);
    return false;
  }
};

/**
 * Cancel a subscription
 * @param userId The user's ID
 * @returns True if the subscription was canceled successfully
 */
export const cancelSubscription = async (userId: string): Promise<boolean> => {
  try {
    if (!userId) {
      console.error('No user ID provided');
      return false;
    }

    // Call the cancel_subscription function
    const { data, error } = await supabase.rpc('cancel_subscription', {
      user_uuid: userId
    });

    if (error) {
      console.error('Error canceling subscription:', error);
      return false;
    }

    // If the function returned true, call the API to actually cancel the subscription
    if (data) {
      const response = await fetch(`${API_URL}/cancel-subscription`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({
          userId
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error canceling subscription via API:', errorData);
        return false;
      }

      return true;
    }

    return false;
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return false;
  }
};

/**
 * Check if a subscription is active
 * @param userId The user's ID
 * @returns True if the subscription is active
 */
export const isSubscriptionActive = async (userId: string): Promise<boolean> => {
  try {
    if (!userId) {
      console.error('No user ID provided');
      return false;
    }

    // Call the is_subscription_active function
    const { data, error } = await supabase.rpc('is_subscription_active', {
      user_uuid: userId
    });

    if (error) {
      console.error('Error checking subscription status:', error);
      return false;
    }

    return data || false;
  } catch (error) {
    console.error('Error checking subscription status:', error);
    return false;
  }
};

/**
 * Get the customer portal URL for managing subscriptions
 * @param userId The user's ID
 * @returns The customer portal URL
 */
export const getCustomerPortalUrl = async (userId: string): Promise<string | null> => {
  try {
    if (!userId) {
      console.error('No user ID provided');
      return null;
    }

    // Check if we're in test mode based on the Stripe publishable key
    const isTestMode = STRIPE_PUBLISHABLE_KEY.startsWith('pk_test_');
    console.log(`Stripe mode: ${isTestMode ? 'TEST' : 'LIVE'}`);

    // Use direct customer portal links as a fallback
    const TEST_PORTAL_LINK = 'https://billing.stripe.com/p/login/test_3cIaEXcibbb74Yt0NbafS00';
    const LIVE_PORTAL_LINK = 'https://billing.stripe.com/p/login/cN28wXauL1x48lqfYY';

    // First try to get a personalized portal URL via the API
    try {
      // Get the customer portal URL via API
      const response = await fetch(`${API_URL}/create-customer-portal-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({
          userId,
          returnUrl: getRedirectUrl()
        })
      });

      if (response.ok) {
        const { url } = await response.json();
        console.log('Successfully created customer portal session');
        return url;
      } else {
        // If API fails, log the error but continue to fallback
        try {
          const errorText = await response.text();
          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch (e) {
            errorData = { error: errorText };
          }

          console.error('Error creating customer portal session:', errorData);

          // Check if this is a configuration error
          if (errorData.error && typeof errorData.error === 'string' &&
              errorData.error.includes('No configuration provided')) {
            console.error('Customer portal not configured in Stripe dashboard, using direct link fallback');
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
      }
    } catch (apiError) {
      console.error('API error when creating customer portal session:', apiError);
    }

    // Fallback to direct portal links
    console.log('Using direct customer portal link fallback');
    return isTestMode ? TEST_PORTAL_LINK : LIVE_PORTAL_LINK;
  } catch (error) {
    console.error('Error creating customer portal session:', error);

    // Final fallback - direct links
    const isTestMode = STRIPE_PUBLISHABLE_KEY.startsWith('pk_test_');
    return isTestMode
      ? 'https://billing.stripe.com/p/login/test_3cIaEXcibbb74Yt0NbafS00'
      : 'https://billing.stripe.com/p/login/cN28wXauL1x48lqfYY';
  }
};

/**
 * Fix invalid Stripe customer ID for a user
 * @param userId The user's ID
 * @returns The result of the fix operation
 */
export const fixStripeCustomer = async (userId: string): Promise<{
  success: boolean;
  customerId?: string;
  status?: string;
  email?: string;
  error?: string;
}> => {
  try {
    if (!userId) {
      console.error('No user ID provided');
      return { success: false, error: 'No user ID provided' };
    }

    console.log('Fixing Stripe customer for user:', userId);

    const response = await fetch(`${API_URL}/fix-stripe-customers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
      },
      body: JSON.stringify({
        userId
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error fixing Stripe customer:', errorData);
      return { success: false, error: errorData.error || 'Unknown error' };
    }

    const result = await response.json();
    console.log('Stripe customer fix result:', result);
    return result;
  } catch (error) {
    console.error('Error fixing Stripe customer:', error);
    return { success: false, error: error.message || 'Unknown error' };
  }
};

/**
 * Open the customer portal in a browser
 * @param userId The user's ID
 * @returns True if the browser was opened successfully
 */
export const openCustomerPortal = async (userId: string): Promise<boolean> => {
  try {
    // Get the customer portal URL
    const portalUrl = await getCustomerPortalUrl(userId);

    if (!portalUrl) {
      console.error('No customer portal URL returned');
      return false;
    }

    // Open the portal URL in a browser
    const result = await WebBrowser.openAuthSessionAsync(portalUrl, getRedirectUrl());

    // Check if the browser was opened successfully
    return result.type === 'success';
  } catch (error) {
    console.error('Error opening customer portal:', error);
    return false;
  }
};

/**
 * Check subscription status after checkout
 * @param userId The user's ID
 * @param sessionId Optional Stripe checkout session ID
 * @returns The subscription status
 */
export const checkSubscriptionStatus = async (
  userId: string,
  sessionId?: string
): Promise<{
  subscription_tier: string;
  status: string;
  manually_updated?: boolean;
  already_subscribed?: boolean;
  manually_checked?: boolean;
  error?: string;
}> => {
  try {
    if (!userId) {
      console.error('No user ID provided');
      return { subscription_tier: 'free', status: 'error', error: 'No user ID provided' };
    }

    console.log('Checking subscription status');
    console.log('User ID:', userId);
    console.log('Session ID:', sessionId || 'Not available');

    // Call our fallback function to check subscription status
    const response = await fetch(
      `${API_URL}/check-subscription-status`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({
          userId,
          sessionId,
        }),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error checking subscription status: ${response.status}`, errorText);
      return {
        subscription_tier: 'free',
        status: 'error',
        error: `Error checking subscription status: ${response.status}`
      };
    }

    const data = await response.json();
    console.log('Subscription check response:', data);

    return data;
  } catch (error) {
    console.error('Error checking subscription status:', error);
    return {
      subscription_tier: 'free',
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

