import * as InAppPurchases from 'expo-in-app-purchases';
import { supabase } from '../lib/supabase';
import { Alert, Platform } from 'react-native';

// Apple In-App Purchase Product IDs
// These need to match the product IDs you create in App Store Connect
export const IAP_PRODUCT_IDS = {
  PRO_MONTHLY: 'com.pilllogic.app.pro.monthly',
  PREMIUM_MONTHLY: 'com.pilllogic.app.premium.monthly',
};

// Product information for display
export const IAP_PRODUCTS = {
  [IAP_PRODUCT_IDS.PRO_MONTHLY]: {
    tier: 'pro',
    title: 'Pro Plan',
    description: 'Unlimited pill scans + 5 live scans per day',
    price: '$2.99/month',
  },
  [IAP_PRODUCT_IDS.PREMIUM_MONTHLY]: {
    tier: 'premium',
    title: 'Premium Plan', 
    description: 'Everything unlocked. Unlimited counts. Doctor note analysis.',
    price: '$5.99/month',
  },
};

// Initialize In-App Purchases
export const initializeIAP = async (): Promise<boolean> => {
  try {
    // Only initialize on iOS
    if (Platform.OS !== 'ios') {
      console.log('IAP: Not on iOS, skipping initialization');
      return false;
    }

    console.log('IAP: Initializing In-App Purchases...');
    await InAppPurchases.connectAsync();
    console.log('IAP: Successfully connected to App Store');
    return true;
  } catch (error) {
    console.error('IAP: Failed to initialize:', error);
    return false;
  }
};

// Get available products from App Store
export const getProducts = async (): Promise<InAppPurchases.IAPItemDetails[]> => {
  try {
    console.log('IAP: Fetching products from App Store...');
    const { results, responseCode } = await InAppPurchases.getProductsAsync(
      Object.values(IAP_PRODUCT_IDS)
    );

    if (responseCode === InAppPurchases.IAPResponseCode.OK) {
      console.log('IAP: Successfully fetched products:', results.length);
      return results;
    } else {
      console.error('IAP: Failed to fetch products, response code:', responseCode);
      return [];
    }
  } catch (error) {
    console.error('IAP: Error fetching products:', error);
    return [];
  }
};

// Purchase a subscription
export const purchaseSubscription = async (
  productId: string,
  userId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('IAP: Starting purchase for product:', productId);

    // Check if IAP is available
    if (Platform.OS !== 'ios') {
      return { success: false, message: 'In-App Purchases are only available on iOS.' };
    }

    // Make the purchase
    const { results, responseCode } = await InAppPurchases.purchaseItemAsync(productId);

    if (responseCode === InAppPurchases.IAPResponseCode.OK && results && results.length > 0) {
      const purchase = results[0];
      console.log('IAP: Purchase successful:', purchase.productId);

      // Verify the receipt and update subscription
      const verificationResult = await verifyAndUpdateSubscription(purchase, userId);
      
      if (verificationResult.success) {
        return { success: true, message: 'Subscription activated successfully!' };
      } else {
        return { success: false, message: verificationResult.message };
      }
    } else if (responseCode === InAppPurchases.IAPResponseCode.USER_CANCELED) {
      return { success: false, message: 'Purchase was cancelled.' };
    } else {
      console.error('IAP: Purchase failed, response code:', responseCode);
      return { success: false, message: 'Purchase failed. Please try again.' };
    }
  } catch (error) {
    console.error('IAP: Purchase error:', error);
    return { success: false, message: 'An error occurred during purchase. Please try again.' };
  }
};

// Restore purchases
export const restorePurchases = async (
  userId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('IAP: Restoring purchases...');

    if (Platform.OS !== 'ios') {
      return { success: false, message: 'Purchase restoration is only available on iOS.' };
    }

    const { results, responseCode } = await InAppPurchases.getPurchaseHistoryAsync();

    if (responseCode === InAppPurchases.IAPResponseCode.OK && results && results.length > 0) {
      console.log('IAP: Found', results.length, 'previous purchases');

      // Process each purchase
      let restoredCount = 0;
      for (const purchase of results) {
        const verificationResult = await verifyAndUpdateSubscription(purchase, userId);
        if (verificationResult.success) {
          restoredCount++;
        }
      }

      if (restoredCount > 0) {
        return { success: true, message: `Restored ${restoredCount} subscription(s).` };
      } else {
        return { success: false, message: 'No active subscriptions found to restore.' };
      }
    } else {
      return { success: false, message: 'No previous purchases found.' };
    }
  } catch (error) {
    console.error('IAP: Restore error:', error);
    return { success: false, message: 'Failed to restore purchases. Please try again.' };
  }
};

// Verify receipt and update subscription in database
const verifyAndUpdateSubscription = async (
  purchase: InAppPurchases.InAppPurchase,
  userId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('IAP: Verifying purchase receipt...');

    // Get product info
    const productInfo = IAP_PRODUCTS[purchase.productId];
    if (!productInfo) {
      console.error('IAP: Unknown product ID:', purchase.productId);
      return { success: false, message: 'Unknown product.' };
    }

    // Call Supabase function to verify receipt and update subscription
    const { data, error } = await supabase.rpc('update_apple_subscription', {
      user_uuid: userId,
      product_id: purchase.productId,
      transaction_id: purchase.transactionId,
      receipt_data: purchase.transactionReceipt,
      subscription_tier: productInfo.tier,
      purchase_date: new Date(purchase.transactionDate).toISOString(),
    });

    if (error) {
      console.error('IAP: Database update error:', error);
      return { success: false, message: 'Failed to activate subscription.' };
    }

    console.log('IAP: Subscription updated successfully');
    return { success: true, message: 'Subscription activated!' };
  } catch (error) {
    console.error('IAP: Verification error:', error);
    return { success: false, message: 'Failed to verify purchase.' };
  }
};

// Check subscription status
export const checkSubscriptionStatus = async (
  userId: string
): Promise<{ subscription_tier: string; status: string; expires_at?: string }> => {
  try {
    console.log('IAP: Checking subscription status for user:', userId);

    const { data, error } = await supabase.rpc('get_apple_subscription_status', {
      user_uuid: userId,
    });

    if (error) {
      console.error('IAP: Error checking subscription status:', error);
      return { subscription_tier: 'free', status: 'error' };
    }

    return data || { subscription_tier: 'free', status: 'inactive' };
  } catch (error) {
    console.error('IAP: Subscription status check error:', error);
    return { subscription_tier: 'free', status: 'error' };
  }
};

// Disconnect from IAP service
export const disconnectIAP = async (): Promise<void> => {
  try {
    if (Platform.OS === 'ios') {
      await InAppPurchases.disconnectAsync();
      console.log('IAP: Disconnected from App Store');
    }
  } catch (error) {
    console.error('IAP: Error disconnecting:', error);
  }
};

// Helper function to format price
export const formatPrice = (product: InAppPurchases.IAPItemDetails): string => {
  return product.priceString || product.price?.toString() || 'Price unavailable';
};

// Helper function to get product tier
export const getProductTier = (productId: string): string => {
  const productInfo = IAP_PRODUCTS[productId];
  return productInfo?.tier || 'free';
};
