// Supabase Edge Function for creating Stripe checkout sessions
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';
import Stripe from 'https://esm.sh/stripe@12.0.0';

// Initialize Stripe with your secret key
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
});

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
      status: 204,
    });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    // Parse the request body
    const { priceId, customerId, userId, email, redirectUrl } = await req.json();

    // Validate required parameters
    if (!priceId || !userId || !email) {
      return new Response('Missing required parameters', { status: 400 });
    }

    // Get or create a Stripe customer
    let stripeCustomerId = customerId;

    try {
      // Try to retrieve the customer to check if it exists in the current mode (test/live)
      if (stripeCustomerId) {
        try {
          await stripe.customers.retrieve(stripeCustomerId);
        } catch (error) {
          // If the customer doesn't exist in the current mode, set to null to create a new one
          console.log(`Customer ${stripeCustomerId} not found in current mode, will create new customer`);
          stripeCustomerId = null;
        }
      }

      if (!stripeCustomerId) {
        // Create a new Stripe customer
        const customer = await stripe.customers.create({
          email,
          metadata: {
            userId,
            supabase_user_id: userId
          },
        });
        stripeCustomerId = customer.id;

        // Update the user's profile with the Stripe customer ID
        const { error } = await supabase
          .from('profiles')
          .update({ stripe_customer_id: stripeCustomerId })
          .eq('id', userId);

        if (error) {
          console.error('Error updating user profile with Stripe customer ID:', error);
        }
      } else {
        // Validate existing customer ID
        try {
          await stripe.customers.retrieve(stripeCustomerId);
          console.log('Existing Stripe customer validated:', stripeCustomerId);
        } catch (customerError) {
          console.error('Invalid existing Stripe customer ID:', stripeCustomerId, customerError);

          // Create a new customer if the existing one is invalid
          const customer = await stripe.customers.create({
            email,
            metadata: {
              userId,
              supabase_user_id: userId
            },
          });
          stripeCustomerId = customer.id;

          // Update the user's profile with the new customer ID
          const { error } = await supabase
            .from('profiles')
            .update({ stripe_customer_id: stripeCustomerId })
            .eq('id', userId);

          if (error) {
            console.error('Error updating user profile with new Stripe customer ID:', error);
          } else {
            console.log('Updated user profile with new Stripe customer ID:', stripeCustomerId);
          }
        }
      }
    } catch (error) {
      console.error('Error handling Stripe customer:', error);
      throw error;
    }

    // Always use valid URLs for Stripe
    // Expo development URLs like exp://*************:8081/--/stripe-redirect are not considered valid by Stripe
    // Use the provided redirectUrl if it's valid, otherwise use fallback URLs
    let validSuccessUrl = 'https://pilllogic.com/success';
    let validCancelUrl = 'https://pilllogic.com/cancel';

    // If redirectUrl is provided and is a valid URL, use it as a base
    if (redirectUrl && (redirectUrl.startsWith('http://') || redirectUrl.startsWith('https://'))) {
      // Extract the base URL (domain)
      const urlObj = new URL(redirectUrl);
      const baseUrl = `${urlObj.protocol}//${urlObj.host}`;

      // Use the base URL with success and cancel paths
      validSuccessUrl = `${baseUrl}/success`;
      validCancelUrl = `${baseUrl}/cancel`;
    }

    // Log the URLs for debugging
    console.log(`Original redirect URL: ${redirectUrl}`);
    console.log(`Using success URL: ${validSuccessUrl}`);
    console.log(`Using cancel URL: ${validCancelUrl}`);

    // Create a checkout session
    const session = await stripe.checkout.sessions.create({
      customer: stripeCustomerId,
      client_reference_id: userId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: validSuccessUrl,
      cancel_url: validCancelUrl,
      allow_promotion_codes: true,
    });

    // Return the checkout URL
    return new Response(
      JSON.stringify({ checkoutUrl: session.url }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 200,
      }
    );
  } catch (error: any) {
    console.error('Error creating checkout session:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 500,
      }
    );
  }
});
