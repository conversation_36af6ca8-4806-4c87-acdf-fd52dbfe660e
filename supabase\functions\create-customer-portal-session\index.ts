// Supabase Edge Function for creating Stripe customer portal sessions
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';
import Stripe from 'https://esm.sh/stripe@12.0.0';

// Initialize Stripe with your secret key
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
});

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
      status: 204,
    });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    // Parse the request body
    const { userId, returnUrl } = await req.json();

    // Validate required parameters
    if (!userId) {
      return new Response('Missing required parameters', { status: 400 });
    }

    // Get the user's Stripe customer ID
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();

    if (userError || !user || !user.stripe_customer_id) {
      return new Response(
        JSON.stringify({ error: 'User not found or no Stripe customer ID' }),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
          status: 404,
        }
      );
    }

    // Validate and potentially recreate the Stripe customer
    let customerId = user.stripe_customer_id;

    try {
      // First, try to retrieve the customer to validate the ID
      await stripe.customers.retrieve(customerId);
      console.log('Stripe customer validated:', customerId);
    } catch (customerError) {
      console.error('Invalid Stripe customer ID:', customerId, customerError);

      // If customer doesn't exist, create a new one
      console.log('Creating new Stripe customer for user:', userId);
      const newCustomer = await stripe.customers.create({
        email: user.email,
        metadata: {
          userId: userId,
          supabase_user_id: userId
        }
      });

      customerId = newCustomer.id;
      console.log('New Stripe customer created:', customerId);

      // Update the user's profile with the new customer ID
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ stripe_customer_id: customerId })
        .eq('id', userId);

      if (updateError) {
        console.error('Error updating user profile with new Stripe customer ID:', updateError);
      } else {
        console.log('Updated user profile with new Stripe customer ID');
      }
    }

    // Create a customer portal session with the validated/new customer ID
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl || 'https://your-app.com',
    });

    // Return the portal URL
    return new Response(
      JSON.stringify({ url: session.url }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 200,
      }
    );
  } catch (error) {
    console.error('Error creating customer portal session:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 500,
      }
    );
  }
});
